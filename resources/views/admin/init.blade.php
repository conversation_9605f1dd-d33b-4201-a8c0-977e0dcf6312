<script type="text/javascript">
    "use strict";

    $(function() {
        // Date range picker
        if ($('.daterangepicker:not(.inited)').length) {
            $('.daterangepicker:not(.inited)').daterangepicker();
            $('.daterangepicker:not(.inited)').addClass('inited');
        }

        // icon picker
        if ($('.icon-picker:not(.inited)').length) {
            $('.icon-picker:not(.inited)').iconpicker();
            $('.icon-picker:not(.inited)').addClass('inited');
        }

        //Select 2
        if ($('#ajaxModal select.ol-select2:not(.inited)').length) {
            $('#ajaxModal select.ol-select2:not(.inited)').select2({
                dropdownParent: $('#ajaxModal')
            });
            $('#ajaxModal select.ol-select2:not(.inited)').addClass('inited');
        }
        if ($('#right-modal select.ol-select2:not(.inited)').length) {
            $('#right-modal select.ol-select2:not(.inited)').select2({
                dropdownParent: $('#right-modal')
            });
            $('#right-modal select.ol-select2:not(.inited)').addClass('inited');
        }
        if ($('select.ol-select2:not(.inited)').length) {
            $('select.ol-select2:not(.inited)').select2();
            $('select.ol-select2:not(.inited)').addClass('inited');
        }

        if ($('#ajaxModal select.select2:not(.inited)').length) {
            $('#ajaxModal select.select2:not(.inited)').select2({
                dropdownParent: $('#ajaxModal')
            });
            $('#ajaxModal select.select2:not(.inited)').addClass('inited');
        }
        if ($('#right-modal select.select2:not(.inited)').length) {
            $('#right-modal select.select2:not(.inited)').select2({
                dropdownParent: $('#right-modal')
            });
            $('#right-modal select.select2:not(.inited)').addClass('inited');
        }
        if ($('select.select2:not(.inited)').length) {
            $('select.select2:not(.inited)').select2();
            $('select.select2:not(.inited)').addClass('inited');
        }

        //Text editor
        if ($('.text_editor:not(.inited)').length) {
            $('.text_editor:not(.inited)').summernote({
                height: 180, // set editor height
                minHeight: null, // set minimum height of editor
                maxHeight: null, // set maximum height of editor
                focus: false, // set focus to editable area after initializing summernote
                toolbar: [
                    ['style', ['style']],
                    ['font', ['bold', 'italic', 'underline', 'clear']],
                    ['fontsize', ['fontsize']],
                    ['color', ['color']],
                    ['para', ['ul', 'ol', 'paragraph']],
                    ['table', ['table']],
                    ['insert', ['link', 'picture', 'video']],
                    ['view', ['fullscreen', 'codeview']]
                ],
                styleTags: [
                    'p', 'blockquote', 'pre', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6'
                ],
                callbacks: {
                    onPaste: function (e) {
                        // Allow pasting HTML content
                        var bufferText = ((e.originalEvent || e).clipboardData || window.clipboardData).getData('Text');
                        e.preventDefault();

                        // Insert the pasted content as HTML
                        $(this).summernote('pasteHTML', bufferText);
                    }
                }
            });
            $('.text_editor:not(.inited)').addClass('inited');
        }
        //summary, note_for_student, short_description, message, biography, type-msg, id="comment", summernote, address, website_description


        $('.tagify:not(.inited)').each(function(index, element) {
            var tagify = new Tagify(element, {
                placeholder: '{{ get_phrase('Enter your keywords') }}'
            });
            $(element).addClass('inited');
        });

        // Handle save button loading state with click event as backup
        $(document).on('click', '.save-btn[type="submit"]', function() {
            console.log('Save button clicked'); // Debug log
            var btn = $(this);
            // Show loading state immediately on click
            btn.find('.btn-text').addClass('d-none');
            btn.find('.btn-loading').removeClass('d-none');
            btn.prop('disabled', true);
            console.log('Loading state activated on click'); // Debug log
        });

        var formElement;
        if ($('.ajaxForm:not(.initialized)').length > 0) {
            $('.ajaxForm:not(.initialized)').ajaxForm({
                beforeSend: function(data, form) {
                    var formElement = $(form);
                    console.log('Ajax beforeSend triggered'); // Debug log
                    // Show loading state when form starts submitting (backup)
                    var saveBtn = $(form).find('.save-btn');
                    if (saveBtn.length > 0) {
                        saveBtn.find('.btn-text').addClass('d-none');
                        saveBtn.find('.btn-loading').removeClass('d-none');
                        saveBtn.prop('disabled', true);
                        console.log('Loading state activated in beforeSend'); // Debug log
                    }
                },
                uploadProgress: function(event, position, total, percentComplete) {},
                complete: function(xhr) {
                    console.log('Ajax complete triggered'); // Debug log
                    // Hide loading state and re-enable button
                    var saveBtn = $('.save-btn');
                    if (saveBtn.length > 0) {
                        saveBtn.find('.btn-text').removeClass('d-none');
                        saveBtn.find('.btn-loading').addClass('d-none');
                        saveBtn.prop('disabled', false);
                        console.log('Loading state deactivated'); // Debug log
                    }

                    setTimeout(function() {
                        distributeServerResponse(xhr.responseText);
                    }, 400);

                    if ($('.ajaxForm.resetable').length > 0) {
                        $('.ajaxForm.resetable')[0].reset();
                    }
                },
                error: function(e) {
                    console.log('Ajax error triggered'); // Debug log
                    // Hide loading state on error
                    var saveBtn = $('.save-btn');
                    if (saveBtn.length > 0) {
                        saveBtn.find('.btn-text').removeClass('d-none');
                        saveBtn.find('.btn-loading').addClass('d-none');
                        saveBtn.prop('disabled', false);
                    }
                    console.log(e);
                }
            });
            $('.ajaxForm:not(.initialized)').addClass('initialized');
        }
    });
</script>
